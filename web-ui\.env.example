# QA Testing Agent Configuration
# Set to true to enable QA testing mode with AI-powered summaries
DEFAULT_QA_MODE=true

# OpenAI Configuration (Required for QA summary generation)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ENDPOINT=https://api.openai.com/v1

# DrCode API Configuration (Optional - for saving QA reports to backend)
DRCODE_API_KEY=your_drcode_api_key_here
DRCODE_API_URL=https://api.drcode.ai/qa-reports

# Other LLM API Keys & Endpoints
ANTHROPIC_ENDPOINT=https://api.anthropic.com
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_API_VERSION=2025-01-01-preview
DEEPSEEK_ENDPOINT=https://api.deepseek.com
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OLLAMA_ENDPOINT=http://localhost:11434
MISTRAL_ENDPOINT=https://api.mistral.ai/v1
MISTRAL_API_KEY=your_mistral_api_key_here
